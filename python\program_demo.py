# 导入必要的库和模块
import serial
import threading
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import struct
import time
import os
import csv
import datetime
import logging

# ====================
# 新协议处理模块
# ====================
class ProtocolHandler:
    """新协议处理类，处理物块颜色和机械臂状态协议"""

    def __init__(self):
        self.latest_protocol_data = {
            'block_color': 0,  # 0=未识别, 1=红色, 2=绿色, 3=蓝色
            'arm_status': 0,   # 0=检测, 1=抓取, 2=运输, 3=分拣
            'last_update_time': 0
        }

    def calc_protocol_checksum(self, cmd, data):
        """计算新协议的校验和

        Args:
            cmd: 指令字节
            data: 数据字节

        Returns:
            校验和字节
        """
        return (cmd + data) & 0xFF

    def create_protocol_packet(self, cmd, data):
        """创建新协议数据包

        Args:
            cmd: 指令 (0x01=物块颜色, 0x02=机械臂状态)
            data: 数据

        Returns:
            完整的数据包字节
        """
        checksum = self.calc_protocol_checksum(cmd, data)
        packet = bytes([0x77, 0x77, cmd, data, checksum])
        return packet

    def parse_protocol_packet(self, packet):
        """解析新协议数据包

        Args:
            packet: 数据包字节

        Returns:
            解析结果字典或None
        """
        try:
            # 检查帧头
            if len(packet) < 5 or packet[0] != 0x77 or packet[1] != 0x77:
                return None

            cmd = packet[2]
            data = packet[3]
            checksum = packet[4]

            # 验证校验和
            expected_checksum = self.calc_protocol_checksum(cmd, data)
            if checksum != expected_checksum:
                print(f"新协议校验和错误: 期望{expected_checksum:02X}，实际{checksum:02X}")
                return None

            result = {
                'cmd': cmd,
                'data': data,
                'timestamp': time.time()
            }

            # 解析具体指令
            if cmd == 0x01:  # 物块颜色指令
                self.latest_protocol_data['block_color'] = data
                self.latest_protocol_data['last_update_time'] = time.time()
                result['type'] = 'block_color'
                if data == 0x00:
                    result['description'] = '未识别到物块'
                elif data == 0x01:
                    result['description'] = '红色物块'
                elif data == 0x02:
                    result['description'] = '绿色物块'
                elif data == 0x03:
                    result['description'] = '蓝色物块'
                else:
                    result['description'] = f'未知颜色代码: {data:02X}'

            elif cmd == 0x02:  # 机械臂状态指令
                self.latest_protocol_data['arm_status'] = data
                self.latest_protocol_data['last_update_time'] = time.time()
                result['type'] = 'arm_status'
                if data == 0x00:
                    result['description'] = '检测状态'
                elif data == 0x01:
                    result['description'] = '抓取状态'
                elif data == 0x02:
                    result['description'] = '运输状态'
                elif data == 0x03:
                    result['description'] = '分拣状态'
                else:
                    result['description'] = f'未知状态代码: {data:02X}'
            else:
                result['type'] = 'unknown'
                result['description'] = f'未知指令: {cmd:02X}'

            return result

        except Exception as e:
            print(f"解析新协议数据包时出错: {e}")
            return None

    def get_block_color_text(self):
        """获取物块颜色文本描述"""
        color_map = {
            0: "未识别",
            1: "红色物块",
            2: "绿色物块",
            3: "蓝色物块"
        }
        return color_map.get(self.latest_protocol_data['block_color'], "未知")

    def get_arm_status_text(self):
        """获取机械臂状态文本描述"""
        status_map = {
            0: "检测状态",
            1: "抓取状态",
            2: "运输状态",
            3: "分拣状态"
        }
        return status_map.get(self.latest_protocol_data['arm_status'], "未知")

    def get_block_color_color(self):
        """获取物块颜色对应的显示颜色"""
        color_map = {
            0: "gray",      # 未识别
            1: "red",       # 红色物块
            2: "green",     # 绿色物块
            3: "blue"       # 蓝色物块
        }
        return color_map.get(self.latest_protocol_data['block_color'], "gray")

    def get_arm_status_color(self):
        """获取机械臂状态对应的显示颜色"""
        color_map = {
            0: "orange",    # 检测状态
            1: "purple",    # 抓取状态
            2: "brown",     # 运输状态
            3: "darkgreen"  # 分拣状态
        }
        return color_map.get(self.latest_protocol_data['arm_status'], "gray")

# ====================
# 串口通信模块
# ====================
class ServoCommunicator:
    def __init__(self, port, baudrate=115200):
        self.ser = serial.Serial(
            port=port,
            baudrate=baudrate,
            bytesize=serial.EIGHTBITS,
            parity=serial.PARITY_NONE,
            stopbits=serial.STOPBITS_ONE,
            timeout=0.1
        )
        self.running = True
        self.data_buffer = bytearray()
        self.latest_data = {
            'position': [0.0]*6,
            'temperature': [0.0]*6,
            'voltage': [0.0]*6,
            'load_status': [0]*6,  # 0=未知, 1=加载(锁定), 2=卸载(释放)
            'error': 0,
            'mode': [0]*6
        }
        self.servo_ids = [1,2,3,4,5,6]  # 假设6个舵机ID

    def calc_checksum(self, data):
        """计算校验和 - 机械臂通信协议

        Args:
            data: 数据字节列表

        Returns:
            校验和字节
        """
        # 计算所有数据字节的和
        checksum = 0
        for b in data:
            checksum += b

        # 取最低字节，然后取反
        return (~(checksum & 0xFF)) & 0xFF

    def send_command(self, servo_id, cmd, params=[]):
        """发送命令到舵机

        Args:
            servo_id: 舵机ID
            cmd: 命令代码
            params: 参数列表

        Raises:
            RuntimeError: 如果串口已关闭
        """
        # 检查串口是否已关闭
        if not hasattr(self, 'ser') or not self.ser.is_open:
            raise RuntimeError("串口已关闭")

        try:
            length = len(params) + 3  # Length = Cmd + Params + Checksum
            packet = bytes([0x55, 0x55, servo_id, length, cmd] + params)
            checksum = self.calc_checksum(packet[2:-1])
            packet += bytes([checksum])

            # 只有在串口打开时才写入数据
            if self.ser.is_open:
                self.ser.write(packet)
        except Exception as e:
            # 如果是串口关闭或无效句柄错误，抛出异常
            if "句柄无效" in str(e) or "端口已关闭" in str(e):
                raise RuntimeError(f"串口已关闭或无效: {e}")
            # 其他错误也抛出
            raise

    def read_position(self, servo_id):
        self.send_command(servo_id, 0x02)  # SERVO_POS_READ - 指令值2

    def read_temperature(self, servo_id):
        self.send_command(servo_id, 0x04)  # SERVO_TEMP_READ - 指令值4

    def read_voltage(self, servo_id):
        self.send_command(servo_id, 0x05)  # SERVO_VIN_READ - 指令值5

    def parse_response(self, packet):
        try:
            # 检查帧头
            if packet[0] != 0x55 or packet[1] != 0x55:
                return None

            # 获取ID、长度和命令
            servo_id = packet[2]
            length = packet[3]
            cmd = packet[4]

            # 检查校验和
            expected_checksum = self.calc_checksum(packet[2:-1])
            actual_checksum = packet[-1]

            if expected_checksum != actual_checksum:
                print(f"校验和错误: 期望{hex(expected_checksum)}，实际{hex(actual_checksum)}")
                return None

            # 解析返回数据
            if cmd == 0x1C or cmd == 28:  # 位置 - 指令值28 (0x1C)
                # 数据长度5，参数1：低八位，参数2：高八位
                if len(packet) >= 7:  # 确保有足够的数据
                    pos_low = packet[5]
                    pos_high = packet[6]
                    # 转换为signed short int
                    pos = (pos_high << 8) | pos_low
                    if pos > 32767:  # 处理负值
                        pos = pos - 65536

                    idx = self.servo_ids.index(servo_id)
                    self.latest_data['position'][idx] = pos * 0.24  # 转换为角度
                    print(f"舵机{servo_id}位置: {pos * 0.24:.1f}°")

            elif cmd == 0x1A or cmd == 26:  # 温度 - 指令值26 (0x1A)
                # 数据长度4，参数1：舵机内部当前温度
                if len(packet) >= 6:  # 确保有足够的数据
                    temp = packet[5]
                    idx = self.servo_ids.index(servo_id)
                    self.latest_data['temperature'][idx] = temp
                    print(f"舵机{servo_id}温度: {temp}℃")

            elif cmd == 0x1B or cmd == 27:  # 电压 - 指令值27 (0x1B)
                # 数据长度5，参数1：低八位，参数2：高八位
                if len(packet) >= 7:  # 确保有足够的数据
                    vin_low = packet[5]
                    vin_high = packet[6]
                    vin = (vin_high << 8) | vin_low

                    idx = self.servo_ids.index(servo_id)
                    self.latest_data['voltage'][idx] = vin / 1000.0  # 转换为V
                    print(f"舵机{servo_id}电压: {vin / 1000.0:.1f}V")

            elif cmd == 32:  # SERVO_LOAD_OR_UNLOAD_READ - 舵机加载状态
                load_status = packet[5]
                idx = self.servo_ids.index(servo_id)
                if 'load_status' not in self.latest_data:
                    self.latest_data['load_status'] = [0] * len(self.servo_ids)
                self.latest_data['load_status'][idx] = load_status

                status_text = "未知"
                if load_status == 1:
                    status_text = "加载(锁定)"
                elif load_status == 2:
                    status_text = "卸载(释放)"
                print(f"舵机{servo_id}加载状态: {status_text}")

            return {
                "servo_id": servo_id,
                "cmd": cmd,
                "data": packet[5:-1]
            }

        except Exception as e:
            print(f"解析错误: {e}")
            return None

    def read_thread(self):
        """读取线程，负责从串口读取数据并解析"""
        print("读取线程已启动")

        while self.running:
            try:
                # 检查串口是否已关闭
                if not hasattr(self, 'ser') or not self.ser.is_open:
                    print("串口已关闭，线程退出")
                    return

                # 轮询读取数据
                for sid in self.servo_ids:
                    # 频繁检查是否应该停止
                    if not self.running or not self.ser.is_open:
                        print("检测到停止信号，线程退出")
                        return

                    try:
                        # 只有在串口打开时才发送命令
                        if self.ser.is_open:
                            self.read_position(sid)
                            self.read_temperature(sid)
                            self.read_voltage(sid)
                    except Exception as e:
                        # 如果是串口关闭或无效句柄错误，直接退出线程
                        if "句柄无效" in str(e) or "端口已关闭" in str(e):
                            print(f"串口已关闭或无效: {e}")
                            return
                        print(f"读取舵机{sid}数据时出错: {e}")

                    # 短暂睡眠，以便能够快速响应停止信号
                    time.sleep(0.01)

                # 再次检查是否应该停止
                if not self.running or not self.ser.is_open:
                    print("检测到停止信号，线程退出")
                    return

                # 读取串口数据
                try:
                    # 只有在串口打开且有数据时才读取
                    if self.ser.is_open and self.ser.in_waiting > 0:
                        data = self.ser.read(self.ser.in_waiting)
                        if data:  # 确保读取到了数据
                            self.data_buffer += data

                        # 查找有效数据包
                        while len(self.data_buffer) >= 5 and self.running and self.ser.is_open:
                            # 首先尝试查找新协议数据包 (0x77 0x77)
                            new_protocol_start = self.data_buffer.find(b'\x77\x77')
                            old_protocol_start = self.data_buffer.find(b'\x55\x55')

                            # 确定哪个协议的数据包在前面
                            if new_protocol_start != -1 and (old_protocol_start == -1 or new_protocol_start < old_protocol_start):
                                # 处理新协议数据包
                                if len(self.data_buffer[new_protocol_start:]) >= 5:
                                    packet = self.data_buffer[new_protocol_start:new_protocol_start+5]
                                    result = self.protocol_handler.parse_protocol_packet(packet)
                                    if result:
                                        print(f"收到新协议数据: {result['description']}")
                                    self.data_buffer = self.data_buffer[new_protocol_start+5:]
                                else:
                                    break
                            elif old_protocol_start != -1:
                                # 处理原有协议数据包
                                if len(self.data_buffer[old_protocol_start:]) < 7:
                                    break

                                length = self.data_buffer[old_protocol_start+3]
                                # 检查长度是否合理，防止错误的数据包
                                if length > 20:  # 假设最大数据包长度不超过20
                                    self.data_buffer = self.data_buffer[old_protocol_start+2:]  # 跳过当前包头
                                    continue

                                packet_size = length + 4  # Header(2) + ID + Length + ... + Checksum

                                if len(self.data_buffer[old_protocol_start:]) >= packet_size:
                                    packet = self.data_buffer[old_protocol_start:old_protocol_start+packet_size]
                                    if self.calc_checksum(packet[2:-1]) == packet[-1]:
                                        self.parse_response(packet)
                                    self.data_buffer = self.data_buffer[old_protocol_start+packet_size:]
                                else:
                                    break
                            else:
                                # 没有找到任何有效的帧头，清空缓冲区
                                self.data_buffer.clear()
                                break
                except Exception as e:
                    # 如果是串口关闭或无效句柄错误，直接退出线程
                    if "句柄无效" in str(e) or "端口已关闭" in str(e):
                        print(f"串口已关闭或无效: {e}")
                        return
                    print(f"读取串口数据时出错: {e}")

                # 使用更短的睡眠时间，以便更快地响应停止信号
                for _ in range(5):  # 分成5次小睡眠，总时间为0.05秒
                    if not self.running or not hasattr(self, 'ser') or not self.ser.is_open:
                        print("检测到停止信号，线程退出")
                        return
                    time.sleep(0.01)
            except Exception as e:
                print(f"线程运行时出错: {e}")
                logging.error(f"线程运行时出错: {e}")
                # 如果出现未捕获的异常，检查是否应该退出
                if not self.running or not hasattr(self, 'ser') or not self.ser.is_open:
                    print("检测到停止信号，线程退出")
                    return
                # 短暂等待后继续
                time.sleep(0.05)

        print("半双工通信读取线程正常退出")

    def process_received_data(self):
        """处理接收到的数据"""
        try:
            # 只有在串口打开且有数据时才读取
            if self.ser.is_open and self.ser.in_waiting > 0:
                data = self.ser.read(self.ser.in_waiting)
                if data:  # 确保读取到了数据
                    self.data_buffer += data
                    logging.debug(f"读取到数据: {' '.join([hex(b) for b in data])}")

                # 查找有效数据包
                while len(self.data_buffer) >= 7 and self.running and self.ser.is_open:  # 最小包长度：帧头(2) + ID(1) + 长度(1) + 命令(1) + 参数(1) + 校验和(1)
                    # 查找帧头
                    start = self.data_buffer.find(b'\x55\x55')
                    if start == -1:
                        self.data_buffer.clear()
                        break

                    if len(self.data_buffer[start:]) < 7:
                        break

                    # 获取长度
                    length = self.data_buffer[start+3]

                    # 检查长度是否合理
                    if length > 20:  # 假设最大数据包长度不超过20
                        self.data_buffer = self.data_buffer[start+2:]  # 跳过当前帧头
                        continue

                    # 计算完整包长度：帧头(2) + ID(1) + 长度(1) + 命令(1) + 参数(N) + 校验和(1)
                    packet_size = length + 4  # 帧头(2) + ID(1) + 长度(1) + ... + 校验和(1)

                    if len(self.data_buffer[start:]) >= packet_size:
                        packet = self.data_buffer[start:start+packet_size]
                        # 解析数据包
                        result = self.parse_response(packet)
                        if result:
                            cmd = packet[4]
                            cmd_name = "未知"
                            if cmd == 0x1C or cmd == 28:
                                cmd_name = "位置"
                            elif cmd == 0x1A or cmd == 26:
                                cmd_name = "温度"
                            elif cmd == 0x1B or cmd == 27:
                                cmd_name = "电压"
                            elif cmd == 0x20:
                                cmd_name = "加载状态"

                            logging.debug(f"解析数据包成功: {result}")
                            print(f"收到{cmd_name}数据: {result}")

                        # 无论解析成功与否，都移除已处理的数据
                        self.data_buffer = self.data_buffer[start+packet_size:]
                    else:
                        break  # 数据不足，等待更多数据
        except Exception as e:
            # 如果是串口关闭或无效句柄错误，直接退出
            if "句柄无效" in str(e) or "端口已关闭" in str(e):
                print(f"串口已关闭或无效: {e}")
                raise
            print(f"处理接收数据时出错: {e}")
            logging.error(f"处理接收数据时出错: {e}")

    # 特定命令
    def ping_all_servos(self):
        """Ping所有舵机，测试连接"""
        try:
            for sid in range(1, 7):
                self.send_command(sid, self.CMD_PING)
                time.sleep(0.01)
            return True
        except Exception as e:
            logging.error(f"Ping舵机失败: {e}")
            return False

    def reset_servo(self, servo_id):
        """复位舵机

        Args:
            servo_id: 舵机ID (1-6)

        Returns:
            是否发送成功
        """
        try:
            return self.send_command(servo_id, self.CMD_RESET)
        except Exception as e:
            logging.error(f"复位舵机{servo_id}失败: {e}")
            return False

    def set_position(self, servo_id, position):
        """设置舵机位置

        Args:
            servo_id: 舵机ID (1-6)
            position: 目标位置 (角度)

        Returns:
            是否发送成功
        """
        try:
            # 将位置转换为协议格式 (0.24度/单位)
            pos_value = int(position / 0.24)
            # 低字节在前
            pos_low = pos_value & 0xFF
            pos_high = (pos_value >> 8) & 0xFF

            return self.send_command(servo_id, self.CMD_SET_POSITION, [pos_low, pos_high])
        except Exception as e:
            logging.error(f"设置舵机{servo_id}位置失败: {e}")
            return False

    def read_position(self, servo_id):
        """读取舵机位置

        Args:
            servo_id: 舵机ID (1-6)

        Returns:
            是否发送成功
        """
        try:
            return self.send_command(servo_id, self.CMD_READ_POSITION)
        except Exception as e:
            logging.error(f"读取舵机{servo_id}位置失败: {e}")
            return False

    def read_temperature(self, servo_id):
        """读取舵机温度

        Args:
            servo_id: 舵机ID (1-6)

        Returns:
            是否发送成功
        """
        try:
            return self.send_command(servo_id, self.CMD_READ_TEMP)
        except Exception as e:
            logging.error(f"读取舵机{servo_id}温度失败: {e}")
            return False

    def read_voltage(self, servo_id):
        """读取舵机电压

        Args:
            servo_id: 舵机ID (1-6)

        Returns:
            是否发送成功
        """
        try:
            return self.send_command(servo_id, self.CMD_READ_VOLTAGE)
        except Exception as e:
            logging.error(f"读取舵机{servo_id}电压失败: {e}")
            return False

    def set_mode(self, servo_id, mode):
        """设置舵机模式

        Args:
            servo_id: 舵机ID (1-6)
            mode: 模式 (0: 位置模式, 1: 速度模式, 2: 力矩模式)

        Returns:
            是否发送成功
        """
        try:
            return self.send_command(servo_id, self.CMD_SET_MODE, [mode])
        except Exception as e:
            logging.error(f"设置舵机{servo_id}模式失败: {e}")
            return False

# ====================
# 半双工通信类
# ====================
class HalfDuplexCommunicator(ServoCommunicator):
    """半双工通信类，继承自ServoCommunicator，添加新协议处理"""

    def __init__(self, port, baudrate=115200):
        super().__init__(port, baudrate)

    def start(self):
        """启动通信线程"""
        import threading
        self._read_thread = threading.Thread(target=self.read_thread)
        self._read_thread.daemon = True
        self._read_thread.start()

# ====================
# 演示模式通信类
# ====================
class DemoCommunicator(ServoCommunicator):
    """演示模式通信类，用于生成模拟数据"""

    def __init__(self, port=None, baudrate=None):
        """初始化演示模式"""
        self.running = True
        self.latest_data = {
            'position': [0.0]*6,
            'temperature': [30.0]*6,
            'voltage': [12.0]*6,
            'load_status': [1]*6,
            'error': 0,
            'mode': [0]*6
        }
        self.servo_ids = [1,2,3,4,5,6]
        self._demo_thread = None

        # 添加新协议处理器
        self.protocol_handler = ProtocolHandler()

        # 演示模式的新协议数据
        self._demo_protocol_counter = 0
        
        # 定义目标角度序列
        self.target_angles = [192, 72, 48, 96, 144]  # 所有舵机将依次移动到这些角度
        self.current_angle_index = 0  # 当前角度索引
        self.next_angle_index = 1  # 下一个角度索引
        self.movement_step = 5.0  # 每次位置更新的步长
        self.position_tolerance = 1.0  # 位置达到目标的容差
        
        # 舵机动作控制
        self.current_moving_servo = 0  # 当前正在移动的舵机索引(0-5)
        self.servo_start_times = [0.0]*6  # 每个舵机开始移动的时间
        self.transition_start_time = time.time()  # 角度转换开始时间
        self.servo_delay = 0.1  # 舵机之间的启动延迟
        self.angle_transition_time = 0.8  # 角度转换总时间
        
    def start(self):
        """启动演示数据生成线程"""
        self._demo_thread = threading.Thread(target=self._generate_demo_data)
        self._demo_thread.daemon = True
        self._demo_thread.start()
        
    def stop(self):
        """停止演示"""
        self.running = False
        if self._demo_thread:
            self._demo_thread.join(timeout=1.0)
    
    def _should_servo_move(self, servo_index, current_time):
        """判断指定舵机是否应该开始移动"""
        return current_time - self.transition_start_time >= servo_index * self.servo_delay
    
    def _calculate_target_position(self, current_time, start_pos, target_pos, servo_index):
        """计算舵机在当前时间应该到达的位置"""
        # 计算舵机开始移动后经过的时间
        servo_start_time = self.transition_start_time + servo_index * self.servo_delay
        elapsed_time = current_time - servo_start_time
        
        if elapsed_time <= 0:
            return start_pos
            
        # 计算完成百分比（考虑总转换时间）
        progress = min(1.0, elapsed_time / self.angle_transition_time)
        
        # 线性插值计算当前位置
        return start_pos + (target_pos - start_pos) * progress
    
    def _move_servos(self):
        """控制所有舵机的移动"""
        current_time = time.time()
        current_target = self.target_angles[self.current_angle_index]
        next_target = self.target_angles[self.next_angle_index]
        all_completed = True
        
        # 更新每个舵机的位置
        for i in range(6):
            if self._should_servo_move(i, current_time):
                # 计算目标位置
                target_pos = self._calculate_target_position(
                    current_time,
                    current_target,
                    next_target,
                    i
                )
                
                # 更新舵机位置
                self.latest_data['position'][i] = target_pos
                
                # 检查是否完成移动
                if abs(self.latest_data['position'][i] - next_target) > self.position_tolerance:
                    all_completed = False
        
        # 如果所有舵机都完成移动，准备下一次转换
        if all_completed:
            self.current_angle_index = self.next_angle_index
            self.next_angle_index = (self.next_angle_index + 1) % len(self.target_angles)
            self.transition_start_time = current_time
            
    def _generate_demo_data(self):
        """生成模拟数据的线程函数"""
        import random

        self.transition_start_time = time.time()

        while self.running:
            # 更新舵机位置
            self._move_servos()

            # 温度小幅波动（±0.5°C）
            for i in range(6):
                self.latest_data['temperature'][i] = 30 + random.uniform(-0.5, 0.5)

            # 电压小幅波动
            for i in range(6):
                self.latest_data['voltage'][i] = 12 + random.uniform(-0.2, 0.2)

            # 模拟新协议数据变化
            self._demo_protocol_counter += 1
            if self._demo_protocol_counter % 100 == 0:  # 每2秒更新一次新协议数据
                # 模拟物块颜色变化
                new_color = random.randint(0, 3)
                self.protocol_handler.latest_protocol_data['block_color'] = new_color
                self.protocol_handler.latest_protocol_data['last_update_time'] = time.time()
                print(f"演示模式: 物块颜色变为 {self.protocol_handler.get_block_color_text()}")

            if self._demo_protocol_counter % 150 == 0:  # 每3秒更新一次机械臂状态
                # 模拟机械臂状态变化
                new_status = random.randint(0, 3)
                self.protocol_handler.latest_protocol_data['arm_status'] = new_status
                self.protocol_handler.latest_protocol_data['last_update_time'] = time.time()
                print(f"演示模式: 机械臂状态变为 {self.protocol_handler.get_arm_status_text()}")

            # 延时0.02秒，使移动更平滑
            time.sleep(0.02)

# ====================
# 被动通信模块
# ====================
class PassiveCommunicator(ServoCommunicator):
    """被动通信类，只接收数据而不发送命令"""

    def __init__(self, port, baudrate=115200):
        """初始化被动通信"""
        # 不调用父类初始化，避免实际打开串口
        self.port = port
        self.baudrate = baudrate
        self.running = True
        self.ser = None
        self.data_buffer = bytearray()
        self.latest_data = {
            'position': [0.0]*6,
            'temperature': [30.0]*6,
            'voltage': [12.0]*6,
            'load_status': [1]*6,
            'error': 0,
            'mode': [0]*6
        }
        self.servo_ids = [1,2,3,4,5,6]
        self._read_thread = None
        self._animation_thread = None

        # 角度动画相关变量
        self.target_positions = [0.0]*6  # 目标角度
        self.start_positions = [0.0]*6   # 起始角度
        self.animation_start_times = [0.0]*6  # 动画开始时间
        self.animation_durations = [0.0]*6    # 动画持续时间(ms)
        self.animating = [False]*6  # 是否正在动画中

        # 调试标志
        self.debug_mode = True

        # 添加新协议处理器
        self.protocol_handler = ProtocolHandler()

        logging.info("被动通信模式已初始化")

    def start(self):
        """启动串口监听线程"""
        try:
            self.ser = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=0.1
            )
            
            # 启动读取线程
            self._read_thread = threading.Thread(target=self.read_thread)
            self._read_thread.daemon = True
            self._read_thread.start()
            
            # 启动角度动画线程
            self._animation_thread = threading.Thread(target=self._animation_loop)
            self._animation_thread.daemon = True
            self._animation_thread.start()
            
            logging.info(f"成功打开串口 {self.port}")
            print(f"被动监听模式已启动，监听串口 {self.port}")
            return True
        except Exception as e:
            logging.error(f"启动串口监听失败: {e}")
            if self.ser:
                self.ser.close()
            return False

    def read_thread(self):
        """读取线程，负责从串口读取数据并解析，但不发送任何命令"""
        print("被动通信读取线程已启动")
        logging.info("被动通信读取线程已启动")

        while self.running:
            try:
                # 检查串口是否已关闭
                if not hasattr(self, 'ser') or not self.ser.is_open:
                    print("串口已关闭，线程退出")
                    return

                # 读取串口数据
                try:
                    # 只有在串口打开且有数据时才读取
                    if self.ser.is_open and self.ser.in_waiting > 0:
                        data = self.ser.read(self.ser.in_waiting)
                        if data:  # 确保读取到了数据
                            self.data_buffer.extend(data)
                            if self.debug_mode:
                                hex_data = ' '.join([f"{b:02X}" for b in data])
                                print(f"读取到数据: {hex_data}")
                        
                        # 处理接收到的数据
                        self._process_buffer()
                except Exception as e:
                    # 如果是串口关闭或无效句柄错误，直接退出线程
                    if "句柄无效" in str(e) or "端口已关闭" in str(e):
                        print(f"串口已关闭或无效: {e}")
                        return
                    print(f"读取串口数据时出错: {e}")

                # 使用更短的睡眠时间，以便更快地响应停止信号
                time.sleep(0.01)
            except Exception as e:
                print(f"线程运行时出错: {e}")
                logging.error(f"线程运行时出错: {e}")
                # 如果出现未捕获的异常，检查是否应该退出
                if not self.running or not hasattr(self, 'ser') or not self.ser.is_open:
                    print("检测到停止信号，线程退出")
                    return
                # 短暂等待后继续
                time.sleep(0.05)

    def _animation_loop(self):
        """角度动画线程函数"""
        logging.info("角度动画线程已启动")
        print("角度动画线程已启动")
        
        while self.running:
            try:
                current_time = time.time()
                
                # 更新每个舵机的当前位置
                for i in range(6):
                    if self.animating[i]:
                        # 计算动画已经进行的时间(ms)
                        elapsed_ms = (current_time - self.animation_start_times[i]) * 1000
                        
                        # 如果动画时间已结束
                        if elapsed_ms >= self.animation_durations[i]:
                            self.latest_data['position'][i] = self.target_positions[i]
                            self.animating[i] = False
                            print(f"舵机{i+1}动画完成，到达目标位置: {self.target_positions[i]:.2f}°")
                        else:
                            # 计算当前应该到达的位置（线性插值）
                            progress = elapsed_ms / self.animation_durations[i]
                            current_pos = self.start_positions[i] + (self.target_positions[i] - self.start_positions[i]) * progress
                            self.latest_data['position'][i] = current_pos
                
                # 短暂休眠，使动画更平滑
                time.sleep(0.02)
                
            except Exception as e:
                logging.error(f"角度动画线程出错: {e}")
                print(f"角度动画线程出错: {e}")
                time.sleep(0.1)
        
        logging.info("角度动画线程已退出")

    def _process_buffer(self):
        """处理缓冲区中的数据，查找并解析完整的数据包"""
        # 查找帧头 (0x55 0x55 或 0x77 0x77)
        i = 0
        while i < len(self.data_buffer) - 1:
            # 检查新协议帧头 (0x77 0x77)
            if self.data_buffer[i] == 0x77 and self.data_buffer[i+1] == 0x77:
                # 新协议数据包处理
                if i + 5 <= len(self.data_buffer):
                    packet = self.data_buffer[i:i+5]
                    if self.debug_mode:
                        hex_packet = ' '.join([f"{b:02X}" for b in packet])
                        print(f"找到新协议数据包: {hex_packet}")

                    result = self.protocol_handler.parse_protocol_packet(packet)
                    if result:
                        print(f"收到新协议数据: {result['description']}")

                    # 移除已处理的数据包
                    self.data_buffer = self.data_buffer[i+5:]
                    i = 0  # 重置索引
                    continue
                else:
                    break  # 等待更多数据
            # 检查原有协议帧头 (0x55 0x55)
            elif self.data_buffer[i] == 0x55 and self.data_buffer[i+1] == 0x55:
                # 找到帧头，检查是否有足够的数据读取长度字段
                if i + 3 >= len(self.data_buffer):
                    break  # 等待更多数据
                
                # 获取数据包长度
                length = self.data_buffer[i+3]
                
                # 检查长度是否合理
                if length > 20:  # 假设最大数据包长度不超过20
                    i += 2  # 跳过当前帧头
                    continue
                    
                # 计算完整包长度：帧头(2) + ID(1) + 长度(1) + 命令(1) + 参数(length-2) + 校验和(1)
                # 注意：length包含了命令和校验和，所以总长度是 i + length + 3
                total_packet_size = length + 3
                
                # 检查是否有完整的数据包
                if i + total_packet_size > len(self.data_buffer):
                    break  # 等待更多数据
                
                # 提取完整的数据包
                packet = self.data_buffer[i:i+total_packet_size]
                
                if self.debug_mode:
                    hex_packet = ' '.join([f"{b:02X}" for b in packet])
                    print(f"找到数据包: {hex_packet}")
                
                # 验证校验和 - 使用特殊的校验和计算方法
                # 这里我们尝试几种不同的校验和计算方法
                
                # 方法1: 标准校验和计算
                checksum1 = self._calc_checksum_method1(packet[2:-1])
                
                # 方法2: 只对命令和参数计算校验和
                checksum2 = self._calc_checksum_method2(packet[4:-1])
                
                # 方法3: 对ID、长度、命令和参数计算校验和，但不取反
                checksum3 = sum(packet[2:-1]) & 0xFF
                
                actual_checksum = packet[-1]
                
                if self.debug_mode:
                    print(f"校验和比较: 方法1={checksum1:02X}, 方法2={checksum2:02X}, 方法3={checksum3:02X}, 实际值={actual_checksum:02X}")
                
                # 尝试所有校验和方法
                if actual_checksum == checksum1 or actual_checksum == checksum2 or actual_checksum == checksum3:
                    # 校验和正确，处理数据包
                    self._parse_packet(packet)
                    # 移除已处理的数据包
                    self.data_buffer = self.data_buffer[i+total_packet_size:]
                    i = 0  # 重置索引
                    continue
                else:
                    # 特殊情况：如果是舵机移动命令，尝试直接解析
                    if packet[4] == 0x01 and len(packet) >= 9:
                        print("尝试直接解析舵机移动命令，忽略校验和")
                        self._parse_packet(packet)
                    
                    if self.debug_mode:
                        print(f"校验和错误: 方法1={checksum1:02X}, 方法2={checksum2:02X}, 方法3={checksum3:02X}, 实际值={actual_checksum:02X}")
                        print(f"数据: {' '.join([f'{b:02X}' for b in packet[2:-1]])}")
                    # 校验和错误，移动到下一个字节继续查找
                    i += 1
            else:
                i += 1
        
        # 如果缓冲区过大，清理前面的数据
        if len(self.data_buffer) > 1024:
            self.data_buffer = self.data_buffer[-1024:]

    def _calc_checksum_method1(self, data):
        """计算校验和方法1 - 标准方法
        
        计算所有数据字节的和，取反，再取低8位
        """
        checksum = 0
        for b in data:
            checksum += b
        return (~(checksum & 0xFF)) & 0xFF

    def _calc_checksum_method2(self, data):
        """计算校验和方法2 - 只对命令和参数计算
        
        计算命令和参数字节的和，取反，再取低8位
        """
        checksum = 0
        for b in data:
            checksum += b
        return (~(checksum & 0xFF)) & 0xFF

    def _parse_packet(self, packet):
        """解析数据包"""
        try:
            # 检查帧头
            if packet[0] != 0x55 or packet[1] != 0x55:
                return None
                
            # 提取关键字段
            servo_id = packet[2]
            length = packet[3]
            cmd = packet[4]
            
            # 打印完整的数据包信息
            if self.debug_mode:
                print(f"解析数据包: ID={servo_id}, 长度={length}, 命令={cmd:02X}")
                if len(packet) > 5:
                    params = ' '.join([f"{b:02X}" for b in packet[5:-1]])
                    print(f"参数: {params}")
            
            # 只处理SERVO_MOVE_TIME_WRITE指令（指令值1）
            if cmd == 0x01:
                # 确保数据包长度正确（至少需要包含角度和时间参数）
                if len(packet) >= 9:
                    # 解析角度参数（参数1和2）
                    angle_low = packet[5]
                    angle_high = packet[6]
                    angle_value = (angle_high << 8) | angle_low
                    
                    # 将角度值转换为实际角度（0~1000对应0~240度）
                    angle_degrees = angle_value * 0.24
                    
                    # 解析时间参数（参数3和4）
                    time_low = packet[7]
                    time_high = packet[8]
                    time_value = (time_high << 8) | time_low
                    
                    # 更新舵机动画参数
                    if 1 <= servo_id <= 6:
                        idx = servo_id - 1
                        
                        # 设置动画参数
                        self.start_positions[idx] = self.latest_data['position'][idx]  # 当前位置作为起始位置
                        self.target_positions[idx] = angle_degrees  # 目标角度
                        self.animation_start_times[idx] = time.time()  # 当前时间作为动画开始时间
                        self.animation_durations[idx] = max(time_value, 100)  # 动画持续时间(ms)，至少100ms
                        self.animating[idx] = True  # 标记为正在动画中
                        
                        # 记录日志
                        print(f"舵机{servo_id}接收到角度命令: 从 {self.start_positions[idx]:.2f}° 到 {angle_degrees:.2f}°, 时间: {time_value}ms")
                        logging.info(f"舵机{servo_id}接收到角度命令: 从 {self.start_positions[idx]:.2f}° 到 {angle_degrees:.2f}°, 时间: {time_value}ms")
        except Exception as e:
            logging.error(f"解析数据包时出错: {e}")
            print(f"解析数据包时出错: {e}")
            import traceback
            traceback.print_exc()

    def stop(self):
        """停止串口监听"""
        port_name = self.port  # 保存端口名称以供日志使用
        self.running = False
        
        try:
            if hasattr(self, 'ser') and self.ser and self.ser.is_open:
                self.ser.close()
                logging.info(f"已关闭串口 {port_name}")
                print(f"已关闭串口 {port_name}")
        except Exception as e:
            logging.error(f"关闭串口 {port_name} 时出错: {e}")
            print(f"关闭串口 {port_name} 时出错: {e}")

# ====================
# GUI界面模块
# ====================
class ServoMonitorApp:
    def __init__(self, root):
        self.root = root
        self.comm = None
        self.update_interval = 200  # ms

        # 设置主题和样式
        self.setup_styles()

        # 设置日志
        self.setup_logging()

        # 设置UI
        self.setup_ui()

        # 记录历史数据
        self.history_data = {
            'position': [[] for _ in range(6)],
            'temperature': [[] for _ in range(6)],
            'voltage': [[] for _ in range(6)],
            'timestamp': []
        }
        self.max_history_points = 100  # 最多保存100个历史数据点

        # 日志记录设置
        self.log_enabled = False  # 是否启用日志记录
        self.log_interval = 5  # 日志记录间隔（秒）
        self.last_log_time = 0  # 上次记录日志的时间
        self.log_file = None  # 当前日志文件
        self.csv_writer = None  # CSV写入器
        self.log_file_handle = None  # 日志文件句柄

    def setup_logging(self):
        """设置日志系统"""
        # 创建logs目录（如果不存在）
        logs_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')
        if not os.path.exists(logs_dir):
            os.makedirs(logs_dir)

        # 设置日志格式
        log_format = '%(asctime)s - %(levelname)s - %(message)s'
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=[
                logging.FileHandler(os.path.join(logs_dir, 'app.log')),
                logging.StreamHandler()
            ]
        )

        # 记录应用启动信息
        logging.info("应用程序启动")

    def setup_styles(self):
        """设置自定义样式"""
        style = ttk.Style()

        # 尝试使用更现代的主题
        try:
            style.theme_use('clam')  # 可以尝试 'clam', 'alt', 'default', 'classic'
        except:
            pass

        # 自定义按钮样式
        style.configure('TButton', font=('微软雅黑', 9))
        style.configure('Connect.TButton', background='#4CAF50')
        style.configure('Disconnect.TButton', background='#F44336')
        style.configure('Refresh.TButton', background='#2196F3')

        # 自定义标签样式
        style.configure('TLabel', font=('微软雅黑', 9))
        style.configure('Title.TLabel', font=('微软雅黑', 12, 'bold'))
        style.configure('Status.TLabel', font=('微软雅黑', 10))
        style.configure('Error.TLabel', foreground='red')

        # 自定义框架样式
        style.configure('Card.TFrame', background='#f5f5f5', relief='raised')
        style.configure('Header.TFrame', background='#e0e0e0')

        # 自定义标签框架样式
        style.configure('Servo.TLabelframe', font=('微软雅黑', 9, 'bold'))
        style.configure('Servo.TLabelframe.Label', font=('微软雅黑', 9, 'bold'))

    def setup_ui(self):
        """设置UI界面"""
        self.root.title("总线舵机监控系统")
        self.root.configure(bg='#f0f0f0')  # 设置背景色

        # 添加图标和标题
        header_frame = ttk.Frame(self.root, style='Header.TFrame')
        header_frame.pack(fill=tk.X, padx=10, pady=5)

        title_label = ttk.Label(header_frame, text="总线舵机监控系统", style='Title.TLabel')
        title_label.pack(side=tk.LEFT, padx=10, pady=5)

        # 创建主容器
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 左侧控制面板
        left_frame = ttk.LabelFrame(main_frame, text="控制面板", style='Servo.TLabelframe')
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=5)

        # 串口控制区域
        port_frame = ttk.Frame(left_frame)
        port_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(port_frame, text="选择串口:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.port_var = tk.StringVar()
        self.port_combo = ttk.Combobox(port_frame, textvariable=self.port_var, width=15)
        self.port_combo.grid(row=0, column=1, padx=5, pady=5)

        # 波特率选择
        ttk.Label(port_frame, text="波特率:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.baudrate_var = tk.StringVar(value="115200")
        baudrate_combo = ttk.Combobox(port_frame, textvariable=self.baudrate_var, width=15)
        baudrate_combo.grid(row=1, column=1, padx=5, pady=5)
        baudrate_combo['values'] = ("9600", "19200", "38400", "57600", "115200")

        # 通信模式选择
        ttk.Label(port_frame, text="通信模式:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.comm_mode_var = tk.StringVar(value="半双工通信")
        comm_mode_combo = ttk.Combobox(port_frame, textvariable=self.comm_mode_var, width=15, state="readonly")
        comm_mode_combo.grid(row=2, column=1, padx=5, pady=5)
        comm_mode_combo['values'] = ("半双工通信", "被动监听", "演示模式")  # 添加演示模式选项

        # 按钮区域
        button_frame = ttk.Frame(left_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        self.connect_btn = ttk.Button(button_frame, text="连接", command=self.connect_selected_port)
        self.connect_btn.pack(fill=tk.X, pady=5)

        refresh_btn = ttk.Button(button_frame, text="刷新串口", command=self.refresh_ports)
        refresh_btn.pack(fill=tk.X, pady=5)

        # 日志记录控制区域
        log_frame = ttk.LabelFrame(left_frame, text="数据日志", style='Servo.TLabelframe')
        log_frame.pack(fill=tk.X, padx=10, pady=10)

        # 日志开关
        log_switch_frame = ttk.Frame(log_frame)
        log_switch_frame.pack(fill=tk.X, padx=5, pady=5)

        self.log_var = tk.BooleanVar(value=False)
        log_check = ttk.Checkbutton(log_switch_frame, text="启用数据记录",
                                    variable=self.log_var, command=self.toggle_logging)
        log_check.pack(side=tk.LEFT, padx=5)

        # 日志间隔设置
        log_interval_frame = ttk.Frame(log_frame)
        log_interval_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(log_interval_frame, text="记录间隔(秒):").pack(side=tk.LEFT, padx=5)

        self.interval_var = tk.StringVar(value="5")
        interval_spinbox = ttk.Spinbox(log_interval_frame, from_=1, to=60,
                                      textvariable=self.interval_var, width=5,
                                      command=self.update_log_interval)
        interval_spinbox.pack(side=tk.RIGHT, padx=5)

        # 日志操作按钮
        log_buttons_frame = ttk.Frame(log_frame)
        log_buttons_frame.pack(fill=tk.X, padx=5, pady=5)

        self.start_log_btn = ttk.Button(log_buttons_frame, text="开始新日志",
                                       command=self.start_new_log, state="disabled")
        self.start_log_btn.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        self.export_log_btn = ttk.Button(log_buttons_frame, text="导出日志",
                                        command=self.export_log, state="disabled")
        self.export_log_btn.pack(side=tk.RIGHT, padx=5, fill=tk.X, expand=True)

        # 日志状态显示
        self.log_status_label = ttk.Label(log_frame, text="日志记录: 未启用", foreground="gray")
        self.log_status_label.pack(padx=10, pady=5)

        # 状态显示
        status_frame = ttk.LabelFrame(left_frame, text="连接状态", style='Servo.TLabelframe')
        status_frame.pack(fill=tk.X, padx=10, pady=10)

        self.status_label = ttk.Label(status_frame, text="未连接", foreground="red", style='Status.TLabel')
        self.status_label.pack(padx=10, pady=10)

        # 错误显示
        self.error_label = ttk.Label(left_frame, text="", foreground="red", style='Error.TLabel')
        self.error_label.pack(fill=tk.X, padx=10, pady=5)

        # 创建右侧主容器，使用网格布局
        right_container = ttk.Frame(main_frame)
        right_container.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 上半部分：协议状态显示
        protocol_main_frame = ttk.LabelFrame(right_container, text="系统状态监控", style='Servo.TLabelframe')
        protocol_main_frame.pack(fill=tk.X, padx=5, pady=(5, 10))

        # 创建协议状态的网格布局
        protocol_grid = ttk.Frame(protocol_main_frame)
        protocol_grid.pack(fill=tk.X, padx=15, pady=15)

        # 物块颜色状态区域
        color_section = ttk.Frame(protocol_grid)
        color_section.grid(row=0, column=0, padx=20, pady=10, sticky="w")

        color_title = ttk.Label(color_section, text="物块识别", font=('微软雅黑', 12, 'bold'))
        color_title.pack(anchor="w")

        self.block_color_label = tk.Label(color_section, text="未识别到物块",
                                         font=('微软雅黑', 14, 'bold'),
                                         foreground="gray", background="#f0f0f0")
        self.block_color_label.pack(anchor="w", pady=(5, 0))

        # 机械臂状态区域
        arm_section = ttk.Frame(protocol_grid)
        arm_section.grid(row=0, column=1, padx=20, pady=10, sticky="w")

        arm_title = ttk.Label(arm_section, text="机械臂状态", font=('微软雅黑', 12, 'bold'))
        arm_title.pack(anchor="w")

        self.arm_status_label = tk.Label(arm_section, text="检测状态",
                                        font=('微软雅黑', 14, 'bold'),
                                        foreground="orange", background="#f0f0f0")
        self.arm_status_label.pack(anchor="w", pady=(5, 0))

        # 协议信息显示区域
        info_section = ttk.Frame(protocol_grid)
        info_section.grid(row=1, column=0, columnspan=2, pady=(15, 5), sticky="ew")

        info_title = ttk.Label(info_section, text="协议信息", font=('微软雅黑', 10, 'bold'))
        info_title.pack(anchor="w")

        self.protocol_info_label = ttk.Label(info_section, text="等待数据...",
                                           font=('微软雅黑', 9), foreground="gray")
        self.protocol_info_label.pack(anchor="w", pady=(2, 0))

        # 配置网格权重
        protocol_grid.columnconfigure(0, weight=1)
        protocol_grid.columnconfigure(1, weight=1)

        # 下半部分：舵机状态显示
        servo_frame = ttk.LabelFrame(right_container, text="舵机状态", style='Servo.TLabelframe')
        servo_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建舵机状态网格
        self.servo_frames = []
        self.servo_labels = []
        self.servo_bars = []

        # 创建3x2的网格布局
        for i in range(6):
            row = i // 3
            col = i % 3

            # 每个舵机的卡片式框架
            servo_card = ttk.Frame(servo_frame, style='Card.TFrame')
            servo_card.grid(row=row, column=col, padx=10, pady=10, sticky="nsew")
            self.servo_frames.append(servo_card)

            # 舵机标题
            ttk.Label(servo_card, text=f"舵机 {i+1}", font=('微软雅黑', 10, 'bold')).pack(pady=(5,10))

            # 舵机数据
            data_frame = ttk.Frame(servo_card)
            data_frame.pack(fill=tk.X, padx=5)

            # 位置
            pos_frame = ttk.Frame(data_frame)
            pos_frame.pack(fill=tk.X, pady=2)
            ttk.Label(pos_frame, text="位置:").pack(side=tk.LEFT)
            pos_lbl = ttk.Label(pos_frame, text="0.0°", width=8)
            pos_lbl.pack(side=tk.RIGHT)

            # 位置进度条
            pos_bar = ttk.Progressbar(data_frame, length=100, mode='determinate')
            pos_bar.pack(fill=tk.X, pady=2)
            pos_bar['maximum'] = 360  # 假设舵机角度范围是0-360度

            # 温度
            temp_frame = ttk.Frame(data_frame)
            temp_frame.pack(fill=tk.X, pady=2)
            ttk.Label(temp_frame, text="温度:").pack(side=tk.LEFT)
            temp_lbl = ttk.Label(temp_frame, text="0℃", width=8)
            temp_lbl.pack(side=tk.RIGHT)

            # 温度进度条
            temp_bar = ttk.Progressbar(data_frame, length=100, mode='determinate')
            temp_bar.pack(fill=tk.X, pady=2)
            temp_bar['maximum'] = 100  # 假设温度范围是0-100度

            # 电压
            volt_frame = ttk.Frame(data_frame)
            volt_frame.pack(fill=tk.X, pady=2)
            ttk.Label(volt_frame, text="电压:").pack(side=tk.LEFT)
            volt_lbl = ttk.Label(volt_frame, text="0.0V", width=8)
            volt_lbl.pack(side=tk.RIGHT)

            # 电压进度条
            volt_bar = ttk.Progressbar(data_frame, length=100, mode='determinate')
            volt_bar.pack(fill=tk.X, pady=2)
            volt_bar['maximum'] = 12  # 假设电压范围是0-12V

            # 状态指示灯
            status_frame = ttk.Frame(data_frame)
            status_frame.pack(fill=tk.X, pady=5)
            ttk.Label(status_frame, text="状态:").pack(side=tk.LEFT)
            status_indicator = ttk.Label(status_frame, text="●", foreground="gray")
            status_indicator.pack(side=tk.RIGHT)

            self.servo_labels.append((pos_lbl, temp_lbl, volt_lbl, status_indicator))
            self.servo_bars.append((pos_bar, temp_bar, volt_bar))

        # 设置网格权重，使得卡片能够均匀分布
        for i in range(2):
            servo_frame.rowconfigure(i, weight=1)
        for i in range(3):
            servo_frame.columnconfigure(i, weight=1)

        # 初始化刷新串口列表
        self.refresh_ports()

    def refresh_ports(self):
        """刷新可用串口列表"""
        import serial.tools.list_ports
        ports = [p.device for p in serial.tools.list_ports.comports()]
        self.port_combo['values'] = ports
        if ports and not self.port_var.get():
            self.port_var.set(ports[0])  # 默认选择第一个串口

    def disconnect_thread(self):
        """在单独的线程中断开连接，避免界面卡死"""
        if not self.comm:
            self.root.after(0, self._update_ui_after_disconnect)
            return

        try:
            # 先更新界面状态
            self.status_label.config(text="正在断开连接...", foreground="orange")
            self.connect_btn.config(state="disabled")

            # 获取当前连接的端口名称
            port_name = getattr(self.comm, 'port', '未知')
            
            # 断开连接
            try:
                if self.comm:
                    self.comm.stop()
            except Exception as e:
                logging.error(f"断开连接时出错: {e}")
            finally:
                self.comm = None

            logging.info(f"已断开与 {port_name} 的连接")
            print(f"已断开与 {port_name} 的连接")
        
        except Exception as e:
            logging.error(f"断开连接过程中出错: {e}")
        finally:
            self.root.after(0, self._update_ui_after_disconnect)

    def _update_ui_after_disconnect(self):
        """断开连接后更新UI"""
        self.status_label.config(text="已断开连接", foreground="red")
        self.connect_btn.config(text="连接", state="normal")

    def connect_selected_port(self):
        """连接或断开选中的串口"""
        # 如果按钮被禁用，说明正在处理中，不做任何操作
        if str(self.connect_btn.cget('state')) == 'disabled':
            return

        # 如果已经连接，则断开
        if self.comm:
            disconnect_thread = threading.Thread(target=self.disconnect_thread)
            disconnect_thread.daemon = True
            disconnect_thread.start()
            return

        # 获取通信模式
        comm_mode = self.comm_mode_var.get()
        
        # 如果是演示模式，不需要实际的串口
        if comm_mode == "演示模式":
            port = "DEMO"
            baudrate = 115200
        else:
            # 获取常规串口参数
            port = self.port_var.get()
            if not port:
                self.status_label.config(text="请选择串口", foreground="red")
                return
            
            try:
                baudrate = int(self.baudrate_var.get())
            except:
                baudrate = 115200
                self.baudrate_var.set(str(baudrate))

        # 禁用按钮，防止重复点击
        self.connect_btn.config(state="disabled")
        self.status_label.config(text=f"正在连接到 {port}...", foreground="blue")
        self.root.update_idletasks()

        try:
            # 尝试连接
            self.connect_servos(port, baudrate=baudrate, comm_mode=comm_mode)

            # 连接成功，更新UI
            self.status_label.config(text=f"已连接到 {port} ({baudrate}波特率, {comm_mode}模式)", foreground="green")
            self.connect_btn.config(text="断开", state="normal")
            print(f"成功连接到 {port} 串口，波特率 {baudrate}，{comm_mode}模式")

            # 根据通信模式显示提示信息
            if comm_mode == "被动监听":
                self.error_label.config(text="被动监听模式：等待接收数据，不发送命令", foreground="blue")
            elif comm_mode == "半双工通信":
                self.error_label.config(text="半双工通信模式：使用单线UART与机械臂通信", foreground="blue")
            elif comm_mode == "演示模式":
                self.error_label.config(text="演示模式：使用模拟数据展示界面功能", foreground="blue")

        except Exception as e:
            self.status_label.config(text=f"连接失败: {str(e)[:50]}", foreground="red")
            self.connect_btn.config(text="连接", state="normal")
            print(f"连接失败: {e}")
            import traceback
            traceback.print_exc()

    def toggle_logging(self):
        """切换日志记录状态"""
        self.log_enabled = self.log_var.get()

        if self.log_enabled:
            # 启用日志记录
            self.log_status_label.config(text="日志记录: 已启用", foreground="green")
            self.start_log_btn.config(state="normal")
            self.export_log_btn.config(state="normal")
            logging.info("日志记录功能已启用")
        else:
            # 禁用日志记录
            self.log_status_label.config(text="日志记录: 未启用", foreground="gray")
            self.start_log_btn.config(state="disabled")
            self.export_log_btn.config(state="disabled")

            # 关闭当前日志文件
            self.close_log_file()
            logging.info("日志记录功能已禁用")

    def update_log_interval(self):
        """更新日志记录间隔"""
        try:
            interval = int(self.interval_var.get())
            if interval < 1:
                interval = 1
            elif interval > 60:
                interval = 60

            self.log_interval = interval
            self.interval_var.set(str(interval))
            logging.info(f"日志记录间隔已更新为 {interval} 秒")
        except ValueError:
            # 如果输入不是有效的整数，恢复为默认值
            self.interval_var.set(str(self.log_interval))

    def start_new_log(self):
        """开始新的日志记录"""
        # 关闭当前日志文件
        self.close_log_file()

        # 创建logs目录（如果不存在）
        logs_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')
        if not os.path.exists(logs_dir):
            os.makedirs(logs_dir)

        # 创建新的日志文件名（使用当前时间）
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_file = os.path.join(logs_dir, f"servo_log_{timestamp}.csv")

        try:
            # 打开日志文件
            self.log_file_handle = open(self.log_file, 'w', newline='')
            self.csv_writer = csv.writer(self.log_file_handle)

            # 写入表头
            header = ['Timestamp']
            for i in range(6):
                header.extend([f'Servo{i+1}_Position', f'Servo{i+1}_Temperature', f'Servo{i+1}_Voltage'])
            self.csv_writer.writerow(header)

            # 更新状态
            self.log_status_label.config(text=f"日志记录: {os.path.basename(self.log_file)}", foreground="green")
            logging.info(f"开始新的日志记录: {self.log_file}")

            # 重置上次记录时间
            self.last_log_time = 0

        except Exception as e:
            messagebox.showerror("错误", f"创建日志文件失败: {e}")
            logging.error(f"创建日志文件失败: {e}")
            self.log_file = None
            self.csv_writer = None
            self.log_file_handle = None

    def close_log_file(self):
        """关闭当前日志文件"""
        if self.log_file_handle:
            try:
                self.log_file_handle.close()
                logging.info(f"日志文件已关闭: {self.log_file}")
            except Exception as e:
                logging.error(f"关闭日志文件时出错: {e}")

            self.log_file_handle = None
            self.csv_writer = None

    def export_log(self):
        """导出日志数据"""
        if not self.history_data['timestamp']:
            messagebox.showinfo("提示", "没有可导出的数据")
            return

        # 选择保存位置
        file_path = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")],
            title="导出日志数据"
        )

        if not file_path:
            return  # 用户取消了操作

        try:
            with open(file_path, 'w', newline='') as f:
                writer = csv.writer(f)

                # 写入表头
                header = ['Timestamp']
                for i in range(6):
                    header.extend([f'Servo{i+1}_Position', f'Servo{i+1}_Temperature', f'Servo{i+1}_Voltage'])
                writer.writerow(header)

                # 写入数据
                for j in range(len(self.history_data['timestamp'])):
                    row = [datetime.datetime.fromtimestamp(self.history_data['timestamp'][j]).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]]
                    for i in range(6):
                        row.extend([
                            self.history_data['position'][i][j],
                            self.history_data['temperature'][i][j],
                            self.history_data['voltage'][i][j]
                        ])
                    writer.writerow(row)

            messagebox.showinfo("成功", f"数据已成功导出到 {file_path}")
            logging.info(f"数据已成功导出到 {file_path}")

        except Exception as e:
            messagebox.showerror("错误", f"导出数据失败: {e}")
            logging.error(f"导出数据失败: {e}")

    def log_data(self, data):
        """记录数据到日志文件"""
        if not self.log_enabled or not self.csv_writer:
            return

        current_time = time.time()

        # 检查是否达到记录间隔
        if current_time - self.last_log_time < self.log_interval:
            return

        try:
            # 记录时间戳和所有舵机数据
            timestamp = datetime.datetime.fromtimestamp(current_time).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
            row = [timestamp]

            for i in range(6):
                row.extend([
                    data['position'][i],
                    data['temperature'][i],
                    data['voltage'][i]
                ])

            self.csv_writer.writerow(row)
            self.log_file_handle.flush()  # 确保数据写入文件
            self.last_log_time = current_time

        except Exception as e:
            logging.error(f"记录数据到日志文件时出错: {e}")
            # 出错时关闭日志文件
            self.close_log_file()
            self.log_status_label.config(text="日志记录: 出错", foreground="red")

    def update_ui(self):
        """更新UI显示"""
        current_time = time.time()

        if self.comm:
            data = self.comm.latest_data

            # 添加数据到历史记录
            self.history_data['timestamp'].append(current_time)
            for i in range(6):
                self.history_data['position'][i].append(data['position'][i])
                self.history_data['temperature'][i].append(data['temperature'][i])
                self.history_data['voltage'][i].append(data['voltage'][i])

            # 限制历史数据点数量
            if len(self.history_data['timestamp']) > self.max_history_points:
                self.history_data['timestamp'] = self.history_data['timestamp'][-self.max_history_points:]
                for i in range(6):
                    self.history_data['position'][i] = self.history_data['position'][i][-self.max_history_points:]
                    self.history_data['temperature'][i] = self.history_data['temperature'][i][-self.max_history_points:]
                    self.history_data['voltage'][i] = self.history_data['voltage'][i][-self.max_history_points:]

            # 记录数据到日志文件
            if self.log_enabled:
                self.log_data(data)

            for i in range(6):
                # 获取当前数据
                position = data['position'][i]
                temperature = data['temperature'][i]
                voltage = data['voltage'][i]

                # 更新文本标签
                self.servo_labels[i][0].config(text=f"{position:.1f}°")
                self.servo_labels[i][1].config(text=f"{temperature:.1f}℃")
                self.servo_labels[i][2].config(text=f"{voltage:.1f}V")

                # 更新进度条
                self.servo_bars[i][0]['value'] = min(position, 360)  # 位置进度条
                self.servo_bars[i][1]['value'] = min(temperature, 100)  # 温度进度条
                self.servo_bars[i][2]['value'] = min(voltage, 12)  # 电压进度条

                # 更新状态指示灯颜色
                status_color = "green"  # 默认为绿色（正常）

                # 根据温度设置状态颜色
                if temperature > 70:
                    status_color = "red"  # 温度过高
                elif temperature > 50:
                    status_color = "orange"  # 温度偏高

                # 根据电压设置状态颜色
                if voltage < 6:
                    status_color = "red"  # 电压过低
                elif voltage < 7:
                    status_color = "orange"  # 电压偏低

                self.servo_labels[i][3].config(foreground=status_color)

            # 更新新协议状态显示
            if hasattr(self.comm, 'protocol_handler'):
                protocol_data = self.comm.protocol_handler.latest_protocol_data

                # 更新物块颜色显示
                block_color_text = self.comm.protocol_handler.get_block_color_text()
                block_color_color = self.comm.protocol_handler.get_block_color_color()

                # 根据颜色设置不同的显示文字和颜色
                if protocol_data['block_color'] == 0:
                    display_text = "未识别到物块"
                    text_color = "#808080"  # 灰色
                elif protocol_data['block_color'] == 1:
                    display_text = "红色物块"
                    text_color = "#DC143C"  # 深红色
                elif protocol_data['block_color'] == 2:
                    display_text = "绿色物块"
                    text_color = "#228B22"  # 森林绿
                elif protocol_data['block_color'] == 3:
                    display_text = "蓝色物块"
                    text_color = "#1E90FF"  # 道奇蓝
                else:
                    display_text = "未知状态"
                    text_color = "#808080"

                self.block_color_label.config(text=display_text, foreground=text_color)

                # 更新机械臂状态显示
                arm_status_text = self.comm.protocol_handler.get_arm_status_text()

                # 根据状态设置不同的显示文字和颜色
                if protocol_data['arm_status'] == 0:
                    display_text = "检测状态"
                    text_color = "#FF8C00"  # 深橙色
                elif protocol_data['arm_status'] == 1:
                    display_text = "抓取状态"
                    text_color = "#9932CC"  # 深兰花紫
                elif protocol_data['arm_status'] == 2:
                    display_text = "运输状态"
                    text_color = "#8B4513"  # 马鞍棕色
                elif protocol_data['arm_status'] == 3:
                    display_text = "分拣状态"
                    text_color = "#006400"  # 深绿色
                else:
                    display_text = "未知状态"
                    text_color = "#808080"

                self.arm_status_label.config(text=display_text, foreground=text_color)

                # 更新协议信息显示
                last_update = protocol_data.get('last_update_time', 0)
                if last_update > 0:
                    import datetime
                    update_time = datetime.datetime.fromtimestamp(last_update).strftime("%H:%M:%S")
                    info_text = f"最后更新: {update_time} | 物块: {block_color_text} | 状态: {arm_status_text}"
                    self.protocol_info_label.config(text=info_text, foreground="#333333")
                else:
                    self.protocol_info_label.config(text="等待数据...", foreground="gray")
        else:
            # 如果未连接，将所有状态指示灯设为灰色
            for i in range(6):
                self.servo_labels[i][3].config(foreground="gray")

            # 重置新协议状态显示
            if hasattr(self, 'block_color_label'):
                self.block_color_label.config(text="未识别到物块", foreground="#808080")
                self.arm_status_label.config(text="检测状态", foreground="#808080")
                self.protocol_info_label.config(text="未连接", foreground="gray")

        # 安排下一次更新
        self.root.after(self.update_interval, self.update_ui)

    def connect_servos(self, port, baudrate=115200, comm_mode="半双工通信"):
        """连接到指定串口，如果失败则抛出异常"""
        if self.comm:
            raise RuntimeError("已经连接到一个串口，请先断开")

        try:
            # 根据通信模式创建不同的通信对象
            if comm_mode == "被动监听":
                logging.info(f"使用被动监听模式连接到 {port}，波特率 {baudrate}")
                self.comm = PassiveCommunicator(port, baudrate=baudrate)
            elif comm_mode == "演示模式":
                logging.info("使用演示模式")
                self.comm = DemoCommunicator()
            else:
                logging.info(f"使用半双工通信模式连接到 {port}，波特率 {baudrate}")
                self.comm = HalfDuplexCommunicator(port, baudrate=baudrate)

            # 启动通信线程
            self.comm.start()
            # 启动UI更新
            self.update_ui()
            return True
        except Exception as e:
            if self.comm:
                try:
                    self.comm.stop()
                except Exception as ex:
                    logging.error(f"停止通信时出错: {ex}")
                self.comm = None
            raise

# ====================
# 主程序
# ====================
if __name__ == "__main__":
    root = tk.Tk()
    app = ServoMonitorApp(root)

    # 设置窗口关闭事件处理
    def on_closing():
        """窗口关闭时的处理"""
        try:
            # 关闭日志文件
            if hasattr(app, 'close_log_file'):
                app.close_log_file()

            # 关闭串口连接
            if app.comm:
                app.comm.stop()

            logging.info("应用程序正常退出")
        except Exception as e:
            logging.error(f"应用程序退出时出错: {e}")
        finally:
            root.destroy()

    # 绑定窗口关闭事件
    root.protocol("WM_DELETE_WINDOW", on_closing)

    # 不再自动连接串口，而是通过界面选择
    root.geometry("1000x600")
    root.mainloop()
