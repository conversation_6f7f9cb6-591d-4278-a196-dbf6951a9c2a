#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新协议演示程序 - 彩色文字版本
演示物块颜色和机械臂状态协议的功能，使用彩色文字显示
"""

import tkinter as tk
from tkinter import ttk
import time
import threading

# 导入新协议处理器
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 从主程序导入协议处理器
from program_demo import ProtocolHandler

class ProtocolDemo:
    """新协议演示类 - 彩色文字版本"""
    
    def __init__(self, root):
        self.root = root
        self.protocol_handler = ProtocolHandler()
        self.setup_ui()
        self.update_display()
    
    def setup_ui(self):
        """设置用户界面"""
        self.root.title("新协议演示程序 - 彩色文字版本")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ttk.Label(main_frame, text="物块颜色和机械臂状态协议演示", 
                               font=('微软雅黑', 18, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # 控制面板
        control_frame = ttk.LabelFrame(main_frame, text="控制面板", padding=15)
        control_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 物块颜色控制
        color_control_frame = ttk.Frame(control_frame)
        color_control_frame.pack(fill=tk.X, pady=10)
        
        ttk.Label(color_control_frame, text="物块颜色控制:", 
                 font=('微软雅黑', 12, 'bold')).pack(anchor="w")
        
        color_buttons_frame = ttk.Frame(color_control_frame)
        color_buttons_frame.pack(anchor="w", pady=(5, 0))
        
        ttk.Button(color_buttons_frame, text="未识别", width=12,
                  command=lambda: self.set_block_color(0)).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(color_buttons_frame, text="红色物块", width=12,
                  command=lambda: self.set_block_color(1)).pack(side=tk.LEFT, padx=5)
        ttk.Button(color_buttons_frame, text="绿色物块", width=12,
                  command=lambda: self.set_block_color(2)).pack(side=tk.LEFT, padx=5)
        ttk.Button(color_buttons_frame, text="蓝色物块", width=12,
                  command=lambda: self.set_block_color(3)).pack(side=tk.LEFT, padx=5)
        
        # 机械臂状态控制
        status_control_frame = ttk.Frame(control_frame)
        status_control_frame.pack(fill=tk.X, pady=10)
        
        ttk.Label(status_control_frame, text="机械臂状态控制:", 
                 font=('微软雅黑', 12, 'bold')).pack(anchor="w")
        
        status_buttons_frame = ttk.Frame(status_control_frame)
        status_buttons_frame.pack(anchor="w", pady=(5, 0))
        
        ttk.Button(status_buttons_frame, text="检测状态", width=12,
                  command=lambda: self.set_arm_status(0)).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(status_buttons_frame, text="抓取状态", width=12,
                  command=lambda: self.set_arm_status(1)).pack(side=tk.LEFT, padx=5)
        ttk.Button(status_buttons_frame, text="运输状态", width=12,
                  command=lambda: self.set_arm_status(2)).pack(side=tk.LEFT, padx=5)
        ttk.Button(status_buttons_frame, text="分拣状态", width=12,
                  command=lambda: self.set_arm_status(3)).pack(side=tk.LEFT, padx=5)
        
        # 显示面板
        display_frame = ttk.LabelFrame(main_frame, text="状态显示", padding=20)
        display_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # 物块颜色显示区域
        color_section = ttk.Frame(display_frame)
        color_section.pack(fill=tk.X, pady=15)
        
        ttk.Label(color_section, text="物块识别结果:", 
                 font=('微软雅黑', 14, 'bold')).pack(anchor="w")
        
        self.color_text_label = tk.Label(color_section, text="未识别到物块", 
                                        font=('微软雅黑', 20, 'bold'), 
                                        foreground="#808080", background="#f0f0f0")
        self.color_text_label.pack(anchor="w", pady=(10, 0))
        
        # 机械臂状态显示区域
        status_section = ttk.Frame(display_frame)
        status_section.pack(fill=tk.X, pady=15)
        
        ttk.Label(status_section, text="机械臂工作状态:", 
                 font=('微软雅黑', 14, 'bold')).pack(anchor="w")
        
        self.status_text_label = tk.Label(status_section, text="检测状态", 
                                         font=('微软雅黑', 20, 'bold'), 
                                         foreground="#FF8C00", background="#f0f0f0")
        self.status_text_label.pack(anchor="w", pady=(10, 0))
        
        # 协议信息显示
        info_section = ttk.Frame(display_frame)
        info_section.pack(fill=tk.X, pady=15)
        
        ttk.Label(info_section, text="协议数据包信息:", 
                 font=('微软雅黑', 12, 'bold')).pack(anchor="w")
        
        self.packet_info_label = tk.Label(info_section, text="等待操作...", 
                                         font=('微软雅黑', 11), 
                                         foreground="#333333", background="#f0f0f0",
                                         justify=tk.LEFT)
        self.packet_info_label.pack(anchor="w", pady=(5, 0))
        
        # 协议说明
        info_frame = ttk.LabelFrame(main_frame, text="协议说明", padding=15)
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        info_text = """协议格式: 0x77 0x77 [指令] [数据] [校验和]
物块颜色指令(0x01): 未识别(0x00) | 红色(0x01) | 绿色(0x02) | 蓝色(0x03)
机械臂状态指令(0x02): 检测(0x00) | 抓取(0x01) | 运输(0x02) | 分拣(0x03)
校验和计算: (指令 + 数据) & 0xFF"""
        
        info_label = ttk.Label(info_frame, text=info_text, font=('微软雅黑', 10))
        info_label.pack(anchor="w")
        
        # 自动演示控制
        auto_frame = ttk.Frame(main_frame)
        auto_frame.pack(fill=tk.X)
        
        self.auto_demo_btn = ttk.Button(auto_frame, text="开始自动演示", 
                                       command=self.toggle_auto_demo)
        self.auto_demo_btn.pack(side=tk.LEFT)
        
        ttk.Label(auto_frame, text="自动演示将循环显示不同的状态", 
                 font=('微软雅黑', 9), foreground="gray").pack(side=tk.LEFT, padx=(10, 0))
        
        self.auto_demo_running = False
        self.auto_demo_thread = None
    
    def set_block_color(self, color):
        """设置物块颜色"""
        self.protocol_handler.latest_protocol_data['block_color'] = color
        self.protocol_handler.latest_protocol_data['last_update_time'] = time.time()
        
        # 创建并显示数据包信息
        packet = self.protocol_handler.create_protocol_packet(0x01, color)
        packet_hex = ' '.join([f'{b:02X}' for b in packet])
        
        color_names = {0: "未识别", 1: "红色物块", 2: "绿色物块", 3: "蓝色物块"}
        color_name = color_names.get(color, "未知")
        
        packet_info = f"物块颜色数据包: {packet_hex}\n指令: 0x01 (物块颜色) | 数据: 0x{color:02X} ({color_name}) | 校验和: 0x{packet[4]:02X}"
        self.packet_info_label.config(text=packet_info)
        
        print(f"物块颜色设置为: {color_name}")
        self.update_display()
    
    def set_arm_status(self, status):
        """设置机械臂状态"""
        self.protocol_handler.latest_protocol_data['arm_status'] = status
        self.protocol_handler.latest_protocol_data['last_update_time'] = time.time()
        
        # 创建并显示数据包信息
        packet = self.protocol_handler.create_protocol_packet(0x02, status)
        packet_hex = ' '.join([f'{b:02X}' for b in packet])
        
        status_names = {0: "检测状态", 1: "抓取状态", 2: "运输状态", 3: "分拣状态"}
        status_name = status_names.get(status, "未知")
        
        packet_info = f"机械臂状态数据包: {packet_hex}\n指令: 0x02 (机械臂状态) | 数据: 0x{status:02X} ({status_name}) | 校验和: 0x{packet[4]:02X}"
        self.packet_info_label.config(text=packet_info)
        
        print(f"机械臂状态设置为: {status_name}")
        self.update_display()
    
    def update_display(self):
        """更新显示"""
        protocol_data = self.protocol_handler.latest_protocol_data
        
        # 更新物块颜色显示
        block_color = protocol_data['block_color']
        if block_color == 0:
            text = "未识别到物块"
            color = "#808080"  # 灰色
        elif block_color == 1:
            text = "红色物块"
            color = "#DC143C"  # 深红色
        elif block_color == 2:
            text = "绿色物块"
            color = "#228B22"  # 森林绿
        elif block_color == 3:
            text = "蓝色物块"
            color = "#1E90FF"  # 道奇蓝
        else:
            text = "未知状态"
            color = "#808080"
        
        self.color_text_label.config(text=text, foreground=color)
        
        # 更新机械臂状态显示
        arm_status = protocol_data['arm_status']
        if arm_status == 0:
            text = "检测状态"
            color = "#FF8C00"  # 深橙色
        elif arm_status == 1:
            text = "抓取状态"
            color = "#9932CC"  # 深兰花紫
        elif arm_status == 2:
            text = "运输状态"
            color = "#8B4513"  # 马鞍棕色
        elif arm_status == 3:
            text = "分拣状态"
            color = "#006400"  # 深绿色
        else:
            text = "未知状态"
            color = "#808080"
        
        self.status_text_label.config(text=text, foreground=color)
    
    def toggle_auto_demo(self):
        """切换自动演示模式"""
        if self.auto_demo_running:
            self.auto_demo_running = False
            self.auto_demo_btn.config(text="开始自动演示")
            if self.auto_demo_thread:
                self.auto_demo_thread.join(timeout=1.0)
        else:
            self.auto_demo_running = True
            self.auto_demo_btn.config(text="停止自动演示")
            self.auto_demo_thread = threading.Thread(target=self.auto_demo_loop)
            self.auto_demo_thread.daemon = True
            self.auto_demo_thread.start()
    
    def auto_demo_loop(self):
        """自动演示循环"""
        counter = 0
        while self.auto_demo_running:
            # 每3秒更换物块颜色
            if counter % 3 == 0:
                color = (counter // 3) % 4
                self.root.after(0, lambda c=color: self.set_block_color(c))
            
            # 每4秒更换机械臂状态
            if counter % 4 == 0:
                status = (counter // 4) % 4
                self.root.after(0, lambda s=status: self.set_arm_status(s))
            
            time.sleep(1)
            counter += 1

def main():
    """主函数"""
    root = tk.Tk()
    app = ProtocolDemo(root)
    
    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("程序已停止")

if __name__ == "__main__":
    main()
