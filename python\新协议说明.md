# 新协议功能说明

## 概述

在原有舵机监控系统的基础上，新增了物块颜色识别和机械臂状态监控协议。该协议使用独特的帧头标识，与原有的舵机通信协议并行工作。

## 协议格式

### 基本格式
```
帧头: 0x77 0x77
指令: 1字节
数据: 1字节  
校验和: 1字节
```

总长度：5字节

### 校验和计算
校验和 = (指令 + 数据) & 0xFF

## 指令定义

### 1. 物块颜色指令 (0x01)

用于传输物块颜色识别结果。

**数据定义：**
- `0x00`: 未识别到物块
- `0x01`: 红色物块
- `0x02`: 绿色物块
- `0x03`: 蓝色物块

**示例数据包：**
- 红色物块: `77 77 01 01 02`
- 绿色物块: `77 77 01 02 03`
- 蓝色物块: `77 77 01 03 04`
- 未识别: `77 77 01 00 01`

### 2. 机械臂状态指令 (0x02)

用于传输机械臂当前工作状态。

**数据定义：**
- `0x00`: 检测状态
- `0x01`: 抓取状态
- `0x02`: 运输状态
- `0x03`: 分拣状态

**示例数据包：**
- 检测状态: `77 77 02 00 02`
- 抓取状态: `77 77 02 01 03`
- 运输状态: `77 77 02 02 04`
- 分拣状态: `77 77 02 03 05`

## 功能特性

### 1. 协议兼容性
- 新协议与原有舵机协议完全兼容
- 使用不同的帧头标识 (0x77 0x77 vs 0x55 0x55)
- 可同时处理两种协议的数据包

### 2. UI界面增强
在原有界面右侧新增了协议状态显示区域：

#### 物块颜色显示
- 文字显示当前识别的物块颜色
- 彩色指示器显示对应颜色
  - 灰色：未识别
  - 红色：红色物块
  - 绿色：绿色物块
  - 蓝色：蓝色物块

#### 机械臂状态显示
- 文字显示当前机械臂状态
- 彩色指示器显示对应状态
  - 橙色：检测状态
  - 紫色：抓取状态
  - 棕色：运输状态
  - 深绿色：分拣状态

### 3. 实时更新
- 协议状态实时更新显示
- 支持演示模式下的模拟数据
- 支持被动监听模式下的实际数据接收

## 文件说明

### 主要文件

1. **program_demo.py** - 主程序
   - 新增 `ProtocolHandler` 类处理新协议
   - 修改 UI 界面添加协议状态显示
   - 更新数据处理逻辑支持双协议

2. **test_protocol.py** - 协议测试程序
   - 用于发送测试数据包
   - 支持手动和自动测试模式
   - 可验证协议数据包的正确性

3. **protocol_demo.py** - 协议演示程序
   - 独立的演示界面
   - 可视化协议功能
   - 支持交互式测试

### 新增类和方法

#### ProtocolHandler 类
- `calc_protocol_checksum()` - 计算校验和
- `create_protocol_packet()` - 创建数据包
- `parse_protocol_packet()` - 解析数据包
- `get_block_color_text()` - 获取颜色文字描述
- `get_arm_status_text()` - 获取状态文字描述
- `get_block_color_color()` - 获取颜色显示色彩
- `get_arm_status_color()` - 获取状态显示色彩

## 使用方法

### 1. 运行主程序
```bash
python program_demo.py
```

### 2. 选择通信模式
- **演示模式**: 使用模拟数据，自动变化物块颜色和机械臂状态
- **被动监听**: 监听串口数据，解析新协议数据包
- **半双工通信**: 支持发送和接收新协议数据

### 3. 观察协议状态
在界面右侧的"协议状态"区域可以看到：
- 当前物块颜色及彩色指示器
- 当前机械臂状态及彩色指示器

### 4. 测试协议功能
```bash
# 运行协议测试程序
python test_protocol.py

# 运行协议演示程序
python protocol_demo.py
```

## 技术细节

### 数据包解析流程
1. 在数据缓冲区中查找帧头 `0x77 0x77`
2. 提取指令和数据字节
3. 计算并验证校验和
4. 根据指令类型解析数据内容
5. 更新内部状态数据
6. 触发UI界面更新

### 多协议处理
程序能够同时处理两种协议：
- 优先检查新协议帧头 `0x77 0x77`
- 然后检查原有协议帧头 `0x55 0x55`
- 根据帧头类型调用相应的解析函数

### 演示模式增强
演示模式新增了新协议数据的模拟：
- 每2秒随机变化物块颜色
- 每3秒随机变化机械臂状态
- 在控制台输出变化信息

## 扩展性

该协议设计具有良好的扩展性：
- 可以轻松添加新的指令类型
- 支持更复杂的数据结构
- 可以扩展到更多设备状态监控

## 注意事项

1. 新协议与原有协议使用不同的帧头，确保不会冲突
2. 校验和计算方法简单有效，适合实时通信
3. UI界面更新频率与原有系统保持一致
4. 所有新功能都向后兼容，不影响原有功能

## 故障排除

### 常见问题
1. **协议状态不更新**: 检查串口连接和数据包格式
2. **校验和错误**: 确认校验和计算方法正确
3. **UI显示异常**: 检查是否正确初始化协议处理器

### 调试方法
- 使用测试程序发送标准数据包
- 查看控制台输出的调试信息
- 使用演示程序验证UI功能
